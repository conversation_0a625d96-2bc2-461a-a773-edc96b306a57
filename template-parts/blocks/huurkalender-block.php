<?php
/**
 * Huurkalender Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$subtitle = get_field('subtitle');
$show_calendar = get_field('show_calendar');
$show_booking_form = get_field('show_booking_form');

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('huurkalender-block');

// Build CSS classes
$css_classes = lindenhof_bem_classes('huurkalender-block', '', '', array('lindenhof-block'));
?>

<section 
    id="<?php echo esc_attr($block_id); ?>" 
    class="<?php echo esc_attr($css_classes); ?>"
    <?php echo lindenhof_swup_attributes(); ?>
    data-block-type="huurkalender"
>
    <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'container'); ?>">
        
        <?php if ($title): ?>
            <header class="<?php echo lindenhof_bem_classes('huurkalender-block', 'header'); ?>">
                <h2 class="<?php echo lindenhof_bem_classes('huurkalender-block', 'title'); ?>">
                    <?php echo lindenhof_safe_text($title); ?>
                </h2>
                
                <?php if ($subtitle): ?>
                    <p class="<?php echo lindenhof_bem_classes('huurkalender-block', 'subtitle'); ?>">
                        <?php echo lindenhof_safe_text($subtitle); ?>
                    </p>
                <?php endif; ?>
            </header>
        <?php endif; ?>
        
        <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'content'); ?>">
            
            <?php if ($show_booking_form): ?>
                <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'booking-form'); ?>">
                    
                    <h3 class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-title'); ?>">
                        <?php echo esc_html__('Beschikbaarheid controleren', 'lindenhof'); ?>
                    </h3>
                    
                    <form 
                        id="<?php echo esc_attr($block_id . '-form'); ?>"
                        class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form'); ?>"
                        data-nonce="<?php echo wp_create_nonce('lindenhof_nonce'); ?>"
                    >
                        
                        <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-row'); ?>">
                            
                            <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-field'); ?>">
                                <label 
                                    for="<?php echo esc_attr($block_id . '-arrival'); ?>"
                                    class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-label'); ?>"
                                >
                                    <?php echo esc_html__('Aankomstdatum', 'lindenhof'); ?>
                                </label>
                                <input 
                                    type="date" 
                                    id="<?php echo esc_attr($block_id . '-arrival'); ?>"
                                    name="arrival_date"
                                    class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-input'); ?>"
                                    required
                                    min="<?php echo date('Y-m-d'); ?>"
                                >
                            </div>
                            
                            <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-field'); ?>">
                                <label 
                                    for="<?php echo esc_attr($block_id . '-guests'); ?>"
                                    class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-label'); ?>"
                                >
                                    <?php echo esc_html__('Aantal gasten', 'lindenhof'); ?>
                                </label>
                                <select 
                                    id="<?php echo esc_attr($block_id . '-guests'); ?>"
                                    name="guests"
                                    class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-select'); ?>"
                                    required
                                >
                                    <option value=""><?php echo esc_html__('Selecteer aantal gasten', 'lindenhof'); ?></option>
                                    <?php for ($i = 1; $i <= 12; $i++): ?>
                                        <option value="<?php echo $i; ?>">
                                            <?php echo sprintf(_n('%d gast', '%d gasten', $i, 'lindenhof'), $i); ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            
                            <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-field'); ?>">
                                <label 
                                    for="<?php echo esc_attr($block_id . '-nights'); ?>"
                                    class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-label'); ?>"
                                >
                                    <?php echo esc_html__('Aantal nachten', 'lindenhof'); ?>
                                </label>
                                <select 
                                    id="<?php echo esc_attr($block_id . '-nights'); ?>"
                                    name="nights"
                                    class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-select'); ?>"
                                >
                                    <?php for ($i = 1; $i <= 14; $i++): ?>
                                        <option value="<?php echo $i; ?>" <?php selected($i, 2); ?>>
                                            <?php echo sprintf(_n('%d nacht', '%d nachten', $i, 'lindenhof'), $i); ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            
                        </div>
                        
                        <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-actions'); ?>">
                            <button 
                                type="submit"
                                class="<?php echo lindenhof_bem_classes('huurkalender-block', 'form-submit'); ?>"
                            >
                                <?php echo esc_html__('Beschikbaarheid controleren', 'lindenhof'); ?>
                            </button>
                        </div>
                        
                    </form>
                    
                    <div 
                        id="<?php echo esc_attr($block_id . '-result'); ?>"
                        class="<?php echo lindenhof_bem_classes('huurkalender-block', 'result'); ?>"
                        style="display: none;"
                    ></div>
                    
                </div>
            <?php endif; ?>
            
            <?php if ($show_calendar): ?>
                <div class="<?php echo lindenhof_bem_classes('huurkalender-block', 'calendar'); ?>">
                    
                    <h3 class="<?php echo lindenhof_bem_classes('huurkalender-block', 'calendar-title'); ?>">
                        <?php echo esc_html__('Beschikbaarheidskalender', 'lindenhof'); ?>
                    </h3>
                    
                    <div 
                        id="<?php echo esc_attr($block_id . '-calendar'); ?>"
                        class="<?php echo lindenhof_bem_classes('huurkalender-block', 'calendar-widget'); ?>"
                        data-nonce="<?php echo wp_create_nonce('lindenhof_nonce'); ?>"
                    >
                        <p class="<?php echo lindenhof_bem_classes('huurkalender-block', 'calendar-loading'); ?>">
                            <?php echo esc_html__('Kalender wordt geladen...', 'lindenhof'); ?>
                        </p>
                    </div>
                    
                </div>
            <?php endif; ?>
            
        </div>
        
    </div>
</section>
