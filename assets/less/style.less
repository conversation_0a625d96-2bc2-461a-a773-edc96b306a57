// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: Lindenhof
Author: <PERSON>
Version: 1.0.0
*/

@import 'vw_values.less';
@import 'constants.less'; 
@import 'default.less';  
@import 'typo.less';
@import 'parts/header.less';  
@import 'parts/footer.less';
@import 'parts/openingtimes.less';
@import 'parts/ddsignature.less'; 
 
// include blocks
@import '../../template-parts/blocks/less/images-marquee-block.less';

::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite; 
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

// Swup

.transition-fade {
  .transitionMore(opacity, .75s, 0s, cubic-bezier(.4, 0, .2, 1));
  opacity: 1;
}

html.is-animating .transition-fade {
  opacity: 0;
}

.grecaptcha-badge {
  visibility: hidden;
}