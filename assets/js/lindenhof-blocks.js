/**
 * Lindenhof Blocks JavaScript
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    // Main Lindenhof object
    window.Lindenhof = window.Lindenhof || {};
    
    // Configuration
    Lindenhof.config = {
        debug: false,
        swupEnabled: true,
        gsapEnabled: true,
        animationDuration: 0.6,
        animationEase: 'power2.out'
    };
    
    // Utility functions
    Lindenhof.utils = {
        
        // Debug logging
        log: function(message, data) {
            if (Lindenhof.config.debug && console && console.log) {
                console.log('[Lindenhof] ' + message, data || '');
            }
        },
        
        // Check if element exists
        exists: function(selector) {
            return $(selector).length > 0;
        },
        
        // Generate unique ID
        uniqueId: function(prefix) {
            prefix = prefix || 'lindenhof';
            return prefix + '-' + Math.random().toString(36).substr(2, 9);
        },
        
        // Debounce function
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }
    };
    
    // Block initialization functions
    Lindenhof.blocks = {
        
        // Initialize all blocks
        init: function() {
            Lindenhof.utils.log('Initializing blocks');
            
            this.initIntroBlocks();
            this.initFaciliteitenBlocks();
            this.initAlgemeenBlocks();
            this.initEtenDrinkenBlocks();
            this.initOmgevingBlocks();
            this.initHuurkalenderBlocks();
            this.initImagesMarqueeBlocks();
            this.initContactBlocks();
        },
        
        // Intro blocks
        initIntroBlocks: function() {
            $('.intro-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing intro block', $block.attr('id'));
                
                // Fade in animation
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.intro-block__title, .intro-block__subtitle, .intro-block__content'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 30,
                        opacity: 0,
                        stagger: 0.2,
                        ease: Lindenhof.config.animationEase
                    });
                }
            });
        },
        
        // Faciliteiten blocks
        initFaciliteitenBlocks: function() {
            $('.faciliteiten-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing faciliteiten block', $block.attr('id'));
                
                // Staggered animation for facility items
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.faciliteiten-block__item'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 20,
                        opacity: 0,
                        stagger: 0.1,
                        ease: Lindenhof.config.animationEase,
                        delay: 0.2
                    });
                }
            });
        },
        
        // Algemeen blocks
        initAlgemeenBlocks: function() {
            $('.algemeen-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing algemeen block', $block.attr('id'));
                
                // Fade in content and image
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.algemeen-block__content, .algemeen-block__image'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 30,
                        opacity: 0,
                        stagger: 0.3,
                        ease: Lindenhof.config.animationEase
                    });
                }
            });
        },
        
        // Eten & Drinken blocks
        initEtenDrinkenBlocks: function() {
            $('.eten-drinken-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing eten-drinken block', $block.attr('id'));
                
                // Animate restaurant items
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.eten-drinken-block__restaurant'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 20,
                        opacity: 0,
                        stagger: 0.15,
                        ease: Lindenhof.config.animationEase,
                        delay: 0.2
                    });
                }
            });
        },
        
        // Omgeving blocks
        initOmgevingBlocks: function() {
            $('.omgeving-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing omgeving block', $block.attr('id'));
                
                // Animate activity items
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.omgeving-block__activity'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 20,
                        opacity: 0,
                        stagger: 0.1,
                        ease: Lindenhof.config.animationEase,
                        delay: 0.2
                    });
                }
            });
        },
        
        // Huurkalender blocks
        initHuurkalenderBlocks: function() {
            $('.huurkalender-block').each(function() {
                var $block = $(this);
                var blockId = $block.attr('id');
                Lindenhof.utils.log('Initializing huurkalender block', blockId);
                
                // Initialize booking form
                var $form = $block.find('.huurkalender-block__form');
                if ($form.length) {
                    $form.on('submit', function(e) {
                        e.preventDefault();
                        Lindenhof.huurkalender.submitBookingForm($form);
                    });
                }
                
                // Initialize calendar widget
                var $calendar = $block.find('.huurkalender-block__calendar-widget');
                if ($calendar.length) {
                    Lindenhof.huurkalender.initCalendar($calendar);
                }
                
                // Fade in animation
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.huurkalender-block__booking-form, .huurkalender-block__calendar'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 30,
                        opacity: 0,
                        stagger: 0.3,
                        ease: Lindenhof.config.animationEase
                    });
                }
            });
        },
        
        // Images Marquee blocks
        initImagesMarqueeBlocks: function() {
            $('.images-marquee-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing images-marquee block', $block.attr('id'));
                
                var $marquee = $block.find('.images-marquee-block__marquee');
                if ($marquee.length) {
                    Lindenhof.marquee.init($marquee);
                }
            });
        },
        
        // Contact blocks
        initContactBlocks: function() {
            $('.contact-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing contact block', $block.attr('id'));
                
                // Fade in animation
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.contact-block__info, .contact-block__form, .contact-block__map'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 30,
                        opacity: 0,
                        stagger: 0.2,
                        ease: Lindenhof.config.animationEase
                    });
                }
            });
        }
    };
    
    // Huurkalender functionality
    Lindenhof.huurkalender = {
        
        // Submit booking form
        submitBookingForm: function($form) {
            var $result = $form.siblings('.huurkalender-block__result');
            var $submit = $form.find('.huurkalender-block__form-submit');
            
            // Show loading state
            $submit.prop('disabled', true).text('Controleren...');
            $result.hide();
            
            // Prepare form data
            var formData = {
                action: 'lindenhof_huurkalender_prefilter',
                arrival_date: $form.find('[name="arrival_date"]').val(),
                guests: $form.find('[name="guests"]').val(),
                nights: $form.find('[name="nights"]').val(),
                nonce: $form.data('nonce')
            };
            
            // Make AJAX request
            $.ajax({
                url: lindenhof_ajax.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        var data = response.data;
                        var resultHtml = '<div class="huurkalender-block__result-content">';
                        
                        if (data.availability) {
                            resultHtml += '<p class="huurkalender-block__result-success">Beschikbaar!</p>';
                            if (data.price) {
                                resultHtml += '<p class="huurkalender-block__result-price">Prijs: ' + data.price + '</p>';
                            }
                            if (data.booking_url) {
                                resultHtml += '<a href="' + data.booking_url + '" target="_blank" class="huurkalender-block__result-button">Nu boeken</a>';
                            }
                        } else {
                            resultHtml += '<p class="huurkalender-block__result-error">Niet beschikbaar voor deze periode.</p>';
                        }
                        
                        if (data.message) {
                            resultHtml += '<p class="huurkalender-block__result-message">' + data.message + '</p>';
                        }
                        
                        resultHtml += '</div>';
                        $result.html(resultHtml).show();
                    } else {
                        $result.html('<p class="huurkalender-block__result-error">Er is een fout opgetreden. Probeer het opnieuw.</p>').show();
                    }
                },
                error: function() {
                    $result.html('<p class="huurkalender-block__result-error">Er is een fout opgetreden. Probeer het opnieuw.</p>').show();
                },
                complete: function() {
                    $submit.prop('disabled', false).text('Beschikbaarheid controleren');
                }
            });
        },
        
        // Initialize calendar widget
        initCalendar: function($calendar) {
            var nonce = $calendar.data('nonce');
            var currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
            
            this.loadCalendarMonth($calendar, currentMonth, nonce);
        },
        
        // Load calendar for specific month
        loadCalendarMonth: function($calendar, month, nonce) {
            $.ajax({
                url: lindenhof_ajax.rest_url + 'huurkalender/availability',
                type: 'GET',
                data: {
                    month: month,
                    _wpnonce: nonce
                },
                success: function(response) {
                    if (response.success && response.availability) {
                        // Render calendar HTML (simplified version)
                        var calendarHtml = '<div class="huurkalender-calendar">';
                        calendarHtml += '<h4>Beschikbaarheid voor ' + month + '</h4>';
                        calendarHtml += '<div class="calendar-grid">';
                        
                        for (var date in response.availability) {
                            var status = response.availability[date];
                            var statusClass = 'calendar-day--' + status;
                            calendarHtml += '<div class="calendar-day ' + statusClass + '">' + date.split('-')[2] + '</div>';
                        }
                        
                        calendarHtml += '</div></div>';
                        $calendar.html(calendarHtml);
                    } else {
                        $calendar.html('<p>Kalender kon niet worden geladen.</p>');
                    }
                },
                error: function() {
                    $calendar.html('<p>Kalender kon niet worden geladen.</p>');
                }
            });
        }
    };

    // Marquee functionality
    Lindenhof.marquee = {

        // Initialize marquee
        init: function($marquee) {
            var speed = $marquee.data('speed') || 'medium';
            var direction = $marquee.data('direction') || 'left';

            // Speed mapping
            var speedMap = {
                'slow': 60,
                'medium': 40,
                'fast': 20
            };

            var duration = speedMap[speed] || 40;
            var $track = $marquee.find('.images-marquee-block__marquee-track');

            if ($track.length && window.gsap) {
                // Calculate track width
                var trackWidth = $track[0].scrollWidth / 2; // Divide by 2 because we duplicated images

                // Create infinite loop animation
                var tl = gsap.timeline({ repeat: -1 });

                if (direction === 'left') {
                    tl.fromTo($track, {
                        x: 0
                    }, {
                        x: -trackWidth,
                        duration: duration,
                        ease: 'none'
                    });
                } else {
                    tl.fromTo($track, {
                        x: -trackWidth
                    }, {
                        x: 0,
                        duration: duration,
                        ease: 'none'
                    });
                }

                // Pause on hover
                $marquee.on('mouseenter', function() {
                    tl.pause();
                }).on('mouseleave', function() {
                    tl.resume();
                });
            }
        }
    };

    // Swup integration
    Lindenhof.swup = {

        // Initialize Swup hooks
        init: function() {
            if (!Lindenhof.config.swupEnabled) {
                Lindenhof.utils.log('Swup not enabled');
                return;
            }

            // Check if the main Swup instance exists (pageContainerWrap from main.js)
            if (typeof pageContainerWrap === 'undefined') {
                Lindenhof.utils.log('Main Swup instance (pageContainerWrap) not available');
                return;
            }

            Lindenhof.utils.log('Initializing Swup hooks for Lindenhof blocks');

            // Re-initialize blocks after page transition using the existing Swup instance
            pageContainerWrap.hooks.on('page:view', function() {
                Lindenhof.utils.log('Swup page view - reinitializing blocks');
                Lindenhof.blocks.init();
            });

            // Add page transition animations
            pageContainerWrap.hooks.on('animation:in:start', function() {
                if (window.gsap) {
                    gsap.from('#main', {
                        duration: 0.5,
                        y: 30,
                        opacity: 0,
                        ease: 'power2.out'
                    });
                }
            });
        }
    };

    // Main initialization
    Lindenhof.init = function() {
        Lindenhof.utils.log('Initializing Lindenhof');

        // Initialize Swup first
        Lindenhof.swup.init();

        // Initialize blocks
        Lindenhof.blocks.init();

        // Global event handlers
        $(window).on('resize', Lindenhof.utils.debounce(function() {
            Lindenhof.utils.log('Window resized - reinitializing marquees');
            $('.images-marquee-block__marquee').each(function() {
                Lindenhof.marquee.init($(this));
            });
        }, 250));

        Lindenhof.utils.log('Lindenhof initialization complete');
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        Lindenhof.init();
    });

})(jQuery);
