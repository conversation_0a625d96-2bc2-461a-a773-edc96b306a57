<?php
/**
 * Lindenhof ACF Field Groups
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Make ACF field groups visible in admin menu
add_filter('acf/settings/show_admin', '__return_true');

// Register ACF field groups for Lindenhof blocks
add_action('acf/init', 'lindenhof_register_acf_field_groups');
function lindenhof_register_acf_field_groups() {
    // Check multiple ACF functions to ensure ACF Pro is loaded
    if (!function_exists('acf_add_local_field_group') && !function_exists('acf_add_field_group')) {
        error_log('Lindenhof: ACF Pro not found - no ACF functions available');
        return;
    }

    error_log('Lindenhof: Starting ACF field group registration');

    // Force registration for now (remove check to ensure groups are created)
    // if (get_option('lindenhof_acf_groups_registered')) {
    //     return;
    // }
    
    // Intro Block
    acf_add_local_field_group(array(
        'key' => 'group_lindenhof_intro_block',
        'title' => 'Intro Block',
        'fields' => array(
            array(
                'key' => 'field_intro_title',
                'label' => 'Titel',
                'name' => 'title',
                'type' => 'text',
                'required' => 1,
            ),
            // gallery:
            array(
                'key' => 'field_intro_gallery',
                'label' => 'Galerij',
                'name' => 'gallery',
                'type' => 'gallery',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'min' => 1,
                'max' => 20,
            ),
            array(
                'key' => 'field_intro_subtitle',
                'label' => 'Ondertitel',
                'name' => 'subtitle',
                'type' => 'text',
            ),
            array(
                'key' => 'field_intro_content',
                'label' => 'Inhoud',
                'name' => 'content',
                'type' => 'wysiwyg',
                'toolbar' => 'basic',
                'media_upload' => 0,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/intro-block',
                ),
            ),
        ),
    ));
    
    // Faciliteiten Block
    acf_add_local_field_group(array(
        'key' => 'group_lindenhof_faciliteiten_block',
        'title' => 'Faciliteiten Block',
        'fields' => array(
            array(
                'key' => 'field_faciliteiten_title',
                'label' => 'Titel',
                'name' => 'title',
                'type' => 'text',
                'required' => 1,
            ),
            array(
                'key' => 'field_faciliteiten_items',
                'label' => 'Faciliteiten',
                'name' => 'facilities',
                'type' => 'repeater',
                'layout' => 'table',
                'button_label' => 'Faciliteit toevoegen',
                'sub_fields' => array(
                    array(
                        'key' => 'field_facility_icon',
                        'label' => 'Icoon',
                        'name' => 'icon',
                        'type' => 'image',
                        'return_format' => 'array',
                        'preview_size' => 'thumbnail',
                    ),
                    array(
                        'key' => 'field_facility_title',
                        'label' => 'Titel',
                        'name' => 'title',
                        'type' => 'text',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_facility_description',
                        'label' => 'Beschrijving',
                        'name' => 'description',
                        'type' => 'textarea',
                        'rows' => 3,
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/faciliteiten-block',
                ),
            ),
        ),
    ));
    
    // Algemeen Block
    acf_add_local_field_group(array(
        'key' => 'group_lindenhof_algemeen_block',
        'title' => 'Algemeen Block',
        'fields' => array(
            array(
                'key' => 'field_algemeen_title',
                'label' => 'Titel',
                'name' => 'title',
                'type' => 'text',
                'required' => 1,
            ),
            array(
                'key' => 'field_algemeen_content',
                'label' => 'Inhoud',
                'name' => 'content',
                'type' => 'wysiwyg',
                'toolbar' => 'full',
                'media_upload' => 1,
            ),
            array(
                'key' => 'field_algemeen_image',
                'label' => 'Afbeelding',
                'name' => 'image',
                'type' => 'image',
                'return_format' => 'array',
                'preview_size' => 'medium',
            ),
            array(
                'key' => 'field_algemeen_image_position',
                'label' => 'Afbeelding positie',
                'name' => 'image_position',
                'type' => 'select',
                'choices' => array(
                    'left' => 'Links',
                    'right' => 'Rechts',
                ),
                'default_value' => 'right',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/algemeen-block',
                ),
            ),
        ),
    ));
    
    // Eten & Drinken Block
    acf_add_local_field_group(array(
        'key' => 'group_lindenhof_eten_drinken_block',
        'title' => 'Eten & Drinken Block',
        'fields' => array(
            array(
                'key' => 'field_eten_drinken_title',
                'label' => 'Titel',
                'name' => 'title',
                'type' => 'text',
                'required' => 1,
            ),
            array(
                'key' => 'field_eten_drinken_content',
                'label' => 'Inhoud',
                'name' => 'content',
                'type' => 'wysiwyg',
                'toolbar' => 'full',
                'media_upload' => 1,
            ),
            array(
                'key' => 'field_eten_drinken_restaurants',
                'label' => 'Restaurants',
                'name' => 'restaurants',
                'type' => 'repeater',
                'layout' => 'row',
                'button_label' => 'Restaurant toevoegen',
                'sub_fields' => array(
                    array(
                        'key' => 'field_restaurant_name',
                        'label' => 'Naam',
                        'name' => 'name',
                        'type' => 'text',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_restaurant_description',
                        'label' => 'Beschrijving',
                        'name' => 'description',
                        'type' => 'textarea',
                        'rows' => 3,
                    ),
                    array(
                        'key' => 'field_restaurant_distance',
                        'label' => 'Afstand',
                        'name' => 'distance',
                        'type' => 'text',
                        'placeholder' => 'bijv. 5 minuten lopen',
                    ),
                    array(
                        'key' => 'field_restaurant_link',
                        'label' => 'Website',
                        'name' => 'link',
                        'type' => 'url',
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/eten-drinken-block',
                ),
            ),
        ),
    ));
    
    // Omgeving Block
    acf_add_local_field_group(array(
        'key' => 'group_lindenhof_omgeving_block',
        'title' => 'Omgeving Block',
        'fields' => array(
            array(
                'key' => 'field_omgeving_title',
                'label' => 'Titel',
                'name' => 'title',
                'type' => 'text',
                'required' => 1,
            ),
            array(
                'key' => 'field_omgeving_content',
                'label' => 'Inhoud',
                'name' => 'content',
                'type' => 'wysiwyg',
                'toolbar' => 'full',
                'media_upload' => 1,
            ),
            array(
                'key' => 'field_omgeving_activities',
                'label' => 'Activiteiten',
                'name' => 'activities',
                'type' => 'repeater',
                'layout' => 'table',
                'button_label' => 'Activiteit toevoegen',
                'sub_fields' => array(
                    array(
                        'key' => 'field_activity_name',
                        'label' => 'Naam',
                        'name' => 'name',
                        'type' => 'text',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_activity_description',
                        'label' => 'Beschrijving',
                        'name' => 'description',
                        'type' => 'textarea',
                        'rows' => 2,
                    ),
                    array(
                        'key' => 'field_activity_distance',
                        'label' => 'Afstand',
                        'name' => 'distance',
                        'type' => 'text',
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/omgeving-block',
                ),
            ),
        ),
    ));

    // Huurkalender Block
    acf_add_local_field_group(array(
        'key' => 'group_lindenhof_huurkalender_block',
        'title' => 'Huurkalender Block',
        'fields' => array(
            array(
                'key' => 'field_huurkalender_title',
                'label' => 'Titel',
                'name' => 'title',
                'type' => 'text',
                'required' => 1,
                'default_value' => 'Beschikbaarheid & Boeken',
            ),
            array(
                'key' => 'field_huurkalender_subtitle',
                'label' => 'Ondertitel',
                'name' => 'subtitle',
                'type' => 'text',
            ),
            array(
                'key' => 'field_huurkalender_show_calendar',
                'label' => 'Kalender tonen',
                'name' => 'show_calendar',
                'type' => 'true_false',
                'default_value' => 1,
                'ui' => 1,
            ),
            array(
                'key' => 'field_huurkalender_show_booking_form',
                'label' => 'Boekingsformulier tonen',
                'name' => 'show_booking_form',
                'type' => 'true_false',
                'default_value' => 1,
                'ui' => 1,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/huurkalender-block',
                ),
            ),
        ),
    ));

    // Images Marquee Block
    acf_add_local_field_group(array(
        'key' => 'group_lindenhof_images_marquee_block',
        'title' => 'Images Marquee Block',
        'fields' => array(
            array(
                'key' => 'field_images_marquee_title',
                'label' => 'Titel',
                'name' => 'title',
                'type' => 'text',
            ),
            array(
                'key' => 'field_images_marquee_images',
                'label' => 'Afbeeldingen',
                'name' => 'images',
                'type' => 'gallery',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'min' => 3,
                'max' => 20,
            ),
            array(
                'key' => 'field_images_marquee_speed',
                'label' => 'Snelheid',
                'name' => 'speed',
                'type' => 'select',
                'choices' => array(
                    'slow' => 'Langzaam',
                    'medium' => 'Gemiddeld',
                    'fast' => 'Snel',
                ),
                'default_value' => 'medium',
            ),
            array(
                'key' => 'field_images_marquee_direction',
                'label' => 'Richting',
                'name' => 'direction',
                'type' => 'select',
                'choices' => array(
                    'left' => 'Links',
                    'right' => 'Rechts',
                ),
                'default_value' => 'left',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/images-marquee-block',
                ),
            ),
        ),
    ));

    // Contact Block
    acf_add_local_field_group(array(
        'key' => 'group_lindenhof_contact_block',
        'title' => 'Contact Block',
        'fields' => array(
            array(
                'key' => 'field_contact_title',
                'label' => 'Titel',
                'name' => 'title',
                'type' => 'text',
                'required' => 1,
                'default_value' => 'Contact',
            ),
            array(
                'key' => 'field_contact_content',
                'label' => 'Inhoud',
                'name' => 'content',
                'type' => 'wysiwyg',
                'toolbar' => 'basic',
                'media_upload' => 0,
            ),
            array(
                'key' => 'field_contact_show_form',
                'label' => 'Contactformulier tonen',
                'name' => 'show_form',
                'type' => 'true_false',
                'default_value' => 1,
                'ui' => 1,
            ),
            array(
                'key' => 'field_contact_form_shortcode',
                'label' => 'Formulier shortcode',
                'name' => 'form_shortcode',
                'type' => 'text',
                'placeholder' => '[contact-form-7 id="123"]',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_contact_show_form',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_contact_show_info',
                'label' => 'Contact informatie tonen',
                'name' => 'show_info',
                'type' => 'true_false',
                'default_value' => 1,
                'ui' => 1,
            ),
            array(
                'key' => 'field_contact_show_map',
                'label' => 'Kaart tonen',
                'name' => 'show_map',
                'type' => 'true_false',
                'default_value' => 0,
                'ui' => 1,
            ),
            array(
                'key' => 'field_contact_map_embed',
                'label' => 'Kaart embed code',
                'name' => 'map_embed',
                'type' => 'textarea',
                'rows' => 3,
                'placeholder' => 'Google Maps embed code',
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_contact_show_map',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/contact-block',
                ),
            ),
        ),
    ));

    // Mark field groups as registered
    update_option('lindenhof_acf_groups_registered', true);
    error_log('Lindenhof: ACF field groups registration completed');
}

// Add admin notice to show registration status
add_action('admin_notices', 'lindenhof_acf_registration_notice');
function lindenhof_acf_registration_notice() {
    if (!function_exists('acf_add_local_field_group')) {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>Lindenhof:</strong> ACF Pro is niet actief. Field groups kunnen niet worden geregistreerd.';
        echo '</p></div>';
        return;
    }

    // Show success notice on ACF pages
    $screen = get_current_screen();
    if ($screen && strpos($screen->id, 'acf') !== false) {
        echo '<div class="notice notice-success is-dismissible"><p>';
        echo '<strong>Lindenhof:</strong> ACF field groups zijn geregistreerd. Refresh de pagina als je ze niet ziet.';
        echo '</p></div>';
    }
}

// Function to reset and re-register field groups (for development)
function lindenhof_reset_acf_field_groups() {
    delete_option('lindenhof_acf_groups_registered');
    lindenhof_register_acf_field_groups();
}

// Add admin action to reset field groups if needed
add_action('wp_ajax_lindenhof_reset_acf_groups', 'lindenhof_reset_acf_field_groups');
